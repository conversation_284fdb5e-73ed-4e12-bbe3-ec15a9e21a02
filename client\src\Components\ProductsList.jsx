import ProductItem from './ProductItem';

const ProductsList = ({ products, loading, error }) => {
    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center py-8">
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <p className="font-bold">Error loading products</p>
                    <p className="text-sm">{error}</p>
                </div>
            </div>
        );
    }

    if (!products || products.length === 0) {
        return (
            <div className="text-center py-8">
                <div className="bg-gray-100 border border-gray-300 text-gray-700 px-4 py-3 rounded">
                    <p className="font-bold">No products found</p>
                    <p className="text-sm">There are no products available at the moment.</p>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="mb-6">
                <h2 className="text-3xl font-bold text-gray-800 mb-2">Our Products</h2>
                <p className="text-gray-600">Discover our amazing collection of products</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {products.map((product) => (
                    <ProductItem
                        key={product.id}
                        product={product}
                    />
                ))}
            </div>
        </div>
    );
};

export default ProductsList;
