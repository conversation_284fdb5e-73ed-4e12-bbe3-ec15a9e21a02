import { useEffect } from "react";
import Login from "../Components/LoginInput";
import { useNavigate } from "react-router-dom";
import { auth } from "../Config/firebase";

const LoginPages = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      if (user) {
        navigate("/");
      }
    });

    return () => unsubscribe();
  }, [navigate]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-100 via-blue-200 to-indigo-300 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <Login />
      </div>
    </div>
  );
};

export default LoginPages;
