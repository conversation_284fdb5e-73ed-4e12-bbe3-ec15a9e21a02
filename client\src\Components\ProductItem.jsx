
import { useCart } from '../Context/useCart';

const ProductItem = ({ product }) => {
    const { addToCart } = useCart();
    const imageUrl = product.imageUrl || product.imageURL || product.image_url || product.image;

    const handleAddToCart = () => {
        addToCart(product);
        // Optional: Show feedback to user
        alert(`${product.name} added to cart!`);
    };

    return (
        <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
            <div className="relative h-48 w-full overflow-hidden">
                {imageUrl && imageUrl.trim() !== '' ? (
                    <>
                        <img
                            src={imageUrl}
                            alt={product.name || 'Product image'}
                            className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                            onLoad={() => {
                                console.log('Image loaded successfully:', imageUrl);
                            }}
                            onError={(e) => {
                                console.error('Image failed to load:', imageUrl);
                                console.error('Error event:', e);
                                // Hide the broken image and show placeholder instead
                                e.target.style.display = 'none';
                                const placeholder = e.target.parentElement.querySelector('.image-placeholder');
                                if (placeholder) {
                                    placeholder.style.display = 'flex';
                                }
                            }}
                        />
                        {/* Hidden placeholder for error fallback */}
                        <div className="image-placeholder w-full h-full bg-gray-200 flex items-center justify-center absolute top-0 left-0" style={{display: 'none'}}>
                            <div className="text-center">
                                <svg className="w-12 h-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <p className="text-xs text-gray-500">Image Error</p>
                            </div>
                        </div>
                    </>
                ) : (
                    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                        <div className="text-center">
                            <svg className="w-12 h-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <p className="text-xs text-gray-500">No Image</p>
                        </div>
                    </div>
                )}


                {product.category && (
                    <div className="absolute top-2 left-2">
                        <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full font-medium">
                            {product.category}
                        </span>
                    </div>
                )}
            </div>


            <div className="p-4">
                <div className="flex justify-between items-start mb-2">
                    <h3 className="text-lg font-semibold text-gray-800 truncate">
                        {product.name}
                    </h3>
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                        ID: {product.id}
                    </span>
                </div>

                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {product.description}
                </p>

                <div className="flex justify-between items-center">
                    <span className="text-xl font-bold text-blue-600">
                        Rp {product.price?.toLocaleString('id-ID')}
                    </span>
                    <button
                        onClick={handleAddToCart}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors duration-200 text-sm">
                        Add to Cart
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ProductItem;

