import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { auth } from "../Config/firebase";
import { signOut } from "firebase/auth";

const HomePage = () => {
  const navigate = useNavigate();
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      setUser(user);
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const handleSignOut = async () => {
    try {
      await signOut(auth);
      navigate("/login");
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <nav className="absolute top-0 w-full z-50 bg-transparent">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-20">
            <div className="flex items-center">
              <h1 className="text-3xl font-bold text-white drop-shadow-lg">
                Coffee <span className="text-amber-300">Right</span>
              </h1>
            </div>
            <div className="flex items-center space-x-6">
              {user ? (
                <>
                  <button
                    onClick={() => navigate("/products")}
                    className="text-white hover:text-amber-300 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-300"
                  >
                    Products
                  </button>
                  <span className="text-white/80 text-sm">Welcome, {user.email}</span>
                  <button
                    onClick={handleSignOut}
                    className="bg-amber-600 hover:bg-amber-700 text-white px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Sign Out
                  </button>
                </>
              ) : (
                <>
                  <button
                    onClick={() => navigate("/login")}
                    className="text-white hover:text-amber-300 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-300"
                  >
                    Login
                  </button>
                  <button
                    onClick={() => navigate("/signup")}
                    className="bg-amber-600 hover:bg-amber-700 text-white px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Sign Up
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background Image with Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-amber-900/90 via-amber-800/80 to-amber-900/90"></div>
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-40"
          style={{
            backgroundImage: `url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="coffee-beans" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="8" fill="%23D97706" opacity="0.3"/><circle cx="75" cy="75" r="6" fill="%23B45309" opacity="0.2"/><circle cx="50" cy="80" r="7" fill="%23A16207" opacity="0.25"/></pattern></defs><rect width="100%" height="100%" fill="url(%23coffee-beans)"/></svg>')`
          }}
        ></div>

        {/* Content */}
        <div className="relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto">
          <div className="animate-fade-in-up">
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
              <span className="block">Selamat Datang di</span>
              <span className="block text-amber-300 font-serif italic">Coffee Right</span>
            </h1>

            <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-2xl mx-auto leading-relaxed">
              Nikmati berbagai macam kopi premium dengan harga terjangkau.
              <span className="block mt-2 text-amber-200 font-medium">
                Rasa bintang lima, budget mahasiswa!
              </span>
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button
                onClick={() => navigate(user ? "/products" : "/signup")}
                className="group bg-amber-600 hover:bg-amber-700 text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 shadow-2xl hover:shadow-amber-500/25 hover:scale-105 transform"
              >
                <span className="flex items-center">
                  {user ? "Belanja Sekarang" : "Mulai Berbelanja"}
                  <svg className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </span>
              </button>

              <button
                onClick={() => navigate("/products")}
                className="text-white border-2 border-white/30 hover:border-amber-300 hover:text-amber-300 px-8 py-4 rounded-full text-lg font-medium transition-all duration-300 backdrop-blur-sm"
              >
                Lihat Menu
              </button>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <svg className="w-6 h-6 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-20 bg-gradient-to-b from-amber-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-sm text-amber-600 font-semibold tracking-wide uppercase mb-4">Keunggulan Kami</h2>
            <p className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Mengapa Memilih <span className="text-amber-600">Coffee Right?</span>
            </p>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Kami berkomitmen memberikan pengalaman kopi terbaik dengan kualitas premium dan harga terjangkau
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
            <div className="group text-center p-8 rounded-2xl bg-white shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
              <div className="flex items-center justify-center h-16 w-16 rounded-full bg-gradient-to-br from-amber-500 to-amber-600 text-white mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Pengiriman Cepat</h3>
              <p className="text-gray-600 leading-relaxed">
                Nikmati kopi segar langsung ke rumah Anda dengan layanan pengiriman express yang cepat dan aman.
              </p>
            </div>

            <div className="group text-center p-8 rounded-2xl bg-white shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
              <div className="flex items-center justify-center h-16 w-16 rounded-full bg-gradient-to-br from-amber-500 to-amber-600 text-white mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Kualitas Premium</h3>
              <p className="text-gray-600 leading-relaxed">
                Biji kopi pilihan terbaik dari berbagai daerah di Indonesia, diproses dengan standar internasional.
              </p>
            </div>

            <div className="group text-center p-8 rounded-2xl bg-white shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
              <div className="flex items-center justify-center h-16 w-16 rounded-full bg-gradient-to-br from-amber-500 to-amber-600 text-white mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Harga Terjangkau</h3>
              <p className="text-gray-600 leading-relaxed">
                Kopi berkualitas tinggi dengan harga yang ramah di kantong mahasiswa dan pekerja muda.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Call to Action Section */}
      <div className="bg-gradient-to-r from-amber-800 to-amber-900 py-16">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Siap Merasakan Kopi Terbaik?
          </h2>
          <p className="text-xl text-amber-100 mb-8">
            Bergabunglah dengan ribuan pelanggan yang sudah merasakan kelezatan kopi kami
          </p>
          <button
            onClick={() => navigate(user ? "/products" : "/signup")}
            className="bg-white text-amber-800 hover:bg-amber-50 px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 transform"
          >
            {user ? "Mulai Belanja" : "Daftar Sekarang"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
