import React, { useState } from "react";

const SearchBar = ({ onSearch }) => {
    const [searchTerm, setSearchTerm] = useState('')
    const handleSearchChange = (e) => {
        setSearchTerm(e.target.value)
    }

    const handlerSubmit = (e) => {
        e.preventDefault();
        console.log("Searching for:", searchTerm);
        if (typeof onSearch === 'function') {
            onSearch(searchTerm);
        } else {
            console.error("onSearch is not a function:", onSearch);
        }
    }

    return (
        <div className="flex items-center">
            <form onSubmit={handlerSubmit}>
                <input
                    type="text"
                    placeholder="Search..."
                    className="border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-600"
                    value={searchTerm}
                    onChange={handleSearchChange}
                />
                <button
                    type="submit"
                    className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-600"
                >
                    Search
                </button>
            </form>
        </div>
    );
}

export default SearchBar
