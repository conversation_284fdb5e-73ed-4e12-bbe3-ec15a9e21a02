const searchProductByName = (query, products) => {
    return products.filter((product) =>
        product.name.toLowerCase().includes(query.toLowerCase())
    );
};

const groupProductsByCategory = (products) => {
    return products.reduce((groups, product) => {
        const category = product.category || 'Uncategorized';
        if (!groups[category]) {
            groups[category] = [];
        }
        groups[category].push(product);
        return groups;
    }, {});
};

export { searchProductByName, groupProductsByCategory };
