import React, { useState } from 'react';
import Products from '../Utils/Products/Products';
import SearchBar from '../Components/SearchBar';
import useProducts from '../Utils/Products/useProducts';
import { searchProductByName, groupProductsByCategory } from '../Utils/data/data';

const ProductsPage = () => {
    const { products, error } = useProducts();
    const [filteredProducts, setFilteredProducts] = useState([]);
    const [isSearching, setIsSearching] = useState(false);
    const [showGrouped, setShowGrouped] = useState(false);

    const handleSearch = (query) => {
        if (query.trim() === '') {
            setIsSearching(false);
            return;
        }

        setIsSearching(true);
        const results = searchProductByName(query, products);
        setFilteredProducts(results);
    };

    const toggleGroupView = () => {
        setShowGrouped(!showGrouped);
    };

    if (error) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-md max-w-md">
                    <p className="font-bold">Error loading products</p>
                    <p className="text-sm">{error}</p>
                </div>
            </div>
        );
    }

    const groupedProducts = showGrouped ? groupProductsByCategory(products) : null;

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <header className="bg-white shadow-sm">
                <div className="container mx-auto px-4 py-6 justify-between flex">
                    <h1 className="text-4xl font-bold text-gray-800">Products</h1>
                    <div className="flex items-center gap-4">
                        <button
                            onClick={toggleGroupView}
                            className={`px-4 py-2 rounded-md ${showGrouped ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-800'}`}
                        >
                            {showGrouped ? 'Normal View' : 'Group by Category'}
                        </button>
                        <SearchBar onSearch={handleSearch} />
                    </div>
                </div>
            </header>

            <main>
                {isSearching ? (
                    <div className="container mx-auto px-4 py-8">
                        <h2 className="text-2xl font-semibold mb-4">Search Results</h2>
                        <Products customProducts={filteredProducts} />
                    </div>
                ) : showGrouped ? (
                    <div className="container mx-auto px-4 py-8">
                        <h2 className="text-2xl font-semibold mb-4">Products by Category</h2>
                        {Object.entries(groupedProducts).map(([category, categoryProducts]) => (
                            <div key={category} className="mb-8">
                                <h3 className="text-xl font-medium mb-4 bg-gray-100 p-2 rounded">{category}</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                                    {categoryProducts.map(product => (
                                        <div key={product.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                                            {product.imageUrl ? (
                                                <img src={product.imageUrl} alt={product.name} className="w-full h-48 object-cover" />
                                            ) : (
                                                <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
                                                    <div className="text-center">
                                                        <svg className="w-12 h-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                        </svg>
                                                        <p className="text-xs text-gray-500">No Image</p>
                                                    </div>
                                                </div>
                                            )}
                                            <div className="p-4">
                                                <h3 className="text-lg font-semibold mb-2">{product.name}</h3>
                                                <p className="text-gray-600 mb-2">{product.description}</p>
                                                <p className="text-primary-600 font-bold">Rp {product.price.toLocaleString()}</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>
                ) : (
                    <Products />
                )}
            </main>
        </div>
    );
};

export default ProductsPage;


